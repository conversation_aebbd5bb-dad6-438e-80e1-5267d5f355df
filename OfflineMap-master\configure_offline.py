#!/usr/bin/env python3
"""
Script pour configurer les exemples pour un fonctionnement hors ligne
"""
import os
import re

def update_tile_urls_in_file(file_path, old_url, new_url):
    """
    Remplace les URLs de tuiles dans un fichier
    """
    if not os.path.exists(file_path):
        print(f"Fichier non trouvé: {file_path}")
        return
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remplacer les URLs
    updated_content = content.replace(old_url, new_url)
    
    # Aussi remplacer les variantes avec {s}
    updated_content = re.sub(
        r'http://\{s\}\.tile\.osm\.org/\{z\}/\{x\}/\{y\}\.png',
        new_url,
        updated_content
    )
    
    if content != updated_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"✅ Mis à jour: {file_path}")
    else:
        print(f"⚠️  Aucun changement: {file_path}")

def configure_for_offline():
    """
    Configure tous les exemples pour fonctionner hors ligne
    """
    # URL locale du serveur de tuiles
    local_tile_url = 'http://localhost:8080/tiles/{z}/{x}/{y}.png'
    
    # URLs à remplacer
    urls_to_replace = [
        'http://tile.osm.org/{z}/{x}/{y}.png',
        'http://{s}.tile.osm.org/{z}/{x}/{y}.png',
        'http://tile.openstreetmap.org/{z}/{x}/{y}.png'
    ]
    
    # Fichiers à modifier
    files_to_update = [
        'leaflet_base64_precache_site/map.js',
        'leaflet_base64fr_precache_site/map.js',
        'leaflet_blob_precache_site/map.js',
        'leaflet_idb_sql_site/map.js',
        'mapbox_idb_sql_site/map.js',
        'openlayers_idb_sql_site/map.js'
    ]
    
    print("🔧 Configuration pour fonctionnement hors ligne...")
    
    for file_path in files_to_update:
        print(f"\n--- Traitement de {file_path} ---")
        for old_url in urls_to_replace:
            update_tile_urls_in_file(file_path, old_url, local_tile_url)

def create_offline_index():
    """
    Crée une page d'index pour accéder facilement aux exemples
    """
    index_html = '''<!DOCTYPE html>
<html>
<head>
    <title>OfflineMap - Exemples Hors Ligne</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .example { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .example h3 { margin-top: 0; color: #333; }
        .example a { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 15px; 
                    background: #007cba; color: white; text-decoration: none; border-radius: 3px; }
        .example a:hover { background: #005a87; }
        .status { padding: 10px; margin: 20px 0; border-radius: 5px; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🗺️ OfflineMap - Exemples Hors Ligne</h1>
    
    <div class="status warning">
        <strong>⚠️ Important:</strong> Assurez-vous que le serveur de tuiles local est démarré avec 
        <code>python tile_server.py</code> avant d'utiliser ces exemples.
    </div>
    
    <div class="status info">
        <strong>ℹ️ Info:</strong> Ces exemples fonctionnent entièrement hors ligne une fois les tuiles téléchargées.
    </div>
    
    <div class="example">
        <h3>📍 Leaflet avec IndexedDB/WebSQL</h3>
        <p>Exemple utilisant Leaflet avec stockage local via IndexedDB ou WebSQL</p>
        <a href="leaflet_idb_sql_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <div class="example">
        <h3>📍 Leaflet avec PouchDB (Base64)</h3>
        <p>Exemple utilisant Leaflet avec PouchDB et stockage en Base64</p>
        <a href="leaflet_base64_precache_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <div class="example">
        <h3>📍 Leaflet avec PouchDB (Blob)</h3>
        <p>Exemple utilisant Leaflet avec PouchDB et stockage en Blob</p>
        <a href="leaflet_blob_precache_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <div class="example">
        <h3>📍 Mapbox avec IndexedDB/WebSQL</h3>
        <p>Exemple utilisant Mapbox avec stockage local</p>
        <a href="mapbox_idb_sql_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <div class="example">
        <h3>📍 OpenLayers avec IndexedDB/WebSQL</h3>
        <p>Exemple utilisant OpenLayers avec stockage local</p>
        <a href="openlayers_idb_sql_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <div class="example">
        <h3>📍 Google Maps avec LocalStorage</h3>
        <p>Exemple utilisant Google Maps avec LocalStorage (nécessite clé API)</p>
        <a href="gmaps_localstorage_site/index.html">Ouvrir l'exemple</a>
    </div>
    
    <hr>
    <p><small>🔧 Serveur de tuiles: <code>python tile_server.py</code> | 📁 Tuiles dans: <code>tiles/</code></small></p>
</body>
</html>'''
    
    with open('index.html', 'w', encoding='utf-8') as f:
        f.write(index_html)
    
    print("✅ Page d'index créée: index.html")

if __name__ == "__main__":
    configure_for_offline()
    create_offline_index()
    print("\n🎉 Configuration terminée!")
    print("📋 Étapes suivantes:")
    print("1. Exécutez 'python download_tiles.py' pour télécharger les tuiles")
    print("2. Démarrez le serveur: 'python tile_server.py'")
    print("3. Ouvrez 'index.html' dans votre navigateur")
