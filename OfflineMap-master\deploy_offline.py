#!/usr/bin/env python3
"""
Script de déploiement complet pour VM hors ligne
"""
import os
import shutil
import subprocess
import sys

def create_deployment_package():
    """
    Crée un package de déploiement complet
    """
    print("📦 Création du package de déploiement...")
    
    # C<PERSON>er le dossier de déploiement
    deploy_dir = "offline_map_deployment"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    # Copier tous les fichiers nécessaires
    files_to_copy = [
        "leaflet_idb_sql_site",
        "leaflet_base64_precache_site", 
        "leaflet_base64fr_precache_site",
        "leaflet_blob_precache_site",
        "mapbox_idb_sql_site",
        "openlayers_idb_sql_site",
        "gmaps_localstorage_site",
        "tiles",  # Dossier des tuiles téléchargées
        "tile_server.py",
        "index.html"
    ]
    
    for item in files_to_copy:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(deploy_dir, item))
                print(f"✅ Copié dossier: {item}")
            else:
                shutil.copy2(item, deploy_dir)
                print(f"✅ Copié fichier: {item}")
        else:
            print(f"⚠️  Non trouvé: {item}")
    
    # Créer les scripts de démarrage
    create_startup_scripts(deploy_dir)
    
    print(f"📦 Package créé dans: {deploy_dir}/")
    return deploy_dir

def create_startup_scripts(deploy_dir):
    """
    Crée les scripts de démarrage pour différents OS
    """
    # Script Windows (start.bat)
    windows_script = '''@echo off
echo ========================================
echo    OfflineMap - Serveur Local
echo ========================================
echo.
echo Demarrage du serveur de tuiles...
echo Ouvrez votre navigateur sur: http://localhost:8080
echo.
echo Appuyez sur Ctrl+C pour arreter
echo.
python tile_server.py
pause
'''
    
    with open(os.path.join(deploy_dir, 'start.bat'), 'w') as f:
        f.write(windows_script)
    
    # Script Linux/Mac (start.sh)
    unix_script = '''#!/bin/bash
echo "========================================"
echo "    OfflineMap - Serveur Local"
echo "========================================"
echo ""
echo "Démarrage du serveur de tuiles..."
echo "Ouvrez votre navigateur sur: http://localhost:8080"
echo ""
echo "Appuyez sur Ctrl+C pour arrêter"
echo ""
python3 tile_server.py
'''
    
    with open(os.path.join(deploy_dir, 'start.sh'), 'w') as f:
        f.write(unix_script)
    
    # Rendre le script Unix exécutable
    try:
        os.chmod(os.path.join(deploy_dir, 'start.sh'), 0o755)
    except:
        pass
    
    # README pour le déploiement
    readme = '''# OfflineMap - Déploiement Hors Ligne

## 🚀 Démarrage Rapide

### Windows:
```
double-clic sur start.bat
```

### Linux/Mac:
```
./start.sh
```

### Manuel:
```
python tile_server.py
```

Puis ouvrez votre navigateur sur: http://localhost:8080

## 📁 Structure

- `tiles/` - Tuiles de carte pré-téléchargées
- `*_site/` - Exemples de cartes interactives
- `tile_server.py` - Serveur local de tuiles
- `index.html` - Page d'accueil avec tous les exemples

## 🔧 Configuration

Le serveur démarre par défaut sur le port 8080.
Pour changer le port, modifiez la variable PORT dans tile_server.py

## 📋 Exemples Disponibles

1. **Leaflet + IndexedDB/WebSQL** - Stockage local avancé
2. **Leaflet + PouchDB** - Base de données locale
3. **Mapbox** - Cartes Mapbox hors ligne  
4. **OpenLayers** - Cartes OpenLayers hors ligne
5. **Google Maps** - Avec LocalStorage (nécessite clé API)

## ⚠️ Important

- Aucune connexion internet requise une fois déployé
- Les tuiles sont pré-téléchargées dans le dossier `tiles/`
- Fonctionne sur tout navigateur moderne
- Compatible Windows, Linux, Mac

## 🆘 Dépannage

Si le serveur ne démarre pas:
1. Vérifiez que Python est installé
2. Vérifiez que le port 8080 est libre
3. Lancez manuellement: `python tile_server.py`

Pour ajouter plus de zones:
1. Modifiez les fichiers cache_keys.json
2. Relancez le téléchargement des tuiles
3. Redéployez le package
'''
    
    with open(os.path.join(deploy_dir, 'README.md'), 'w', encoding='utf-8') as f:
        f.write(readme)
    
    print("✅ Scripts de démarrage créés")

def create_archive(deploy_dir):
    """
    Crée une archive du déploiement
    """
    try:
        import zipfile
        
        archive_name = f"{deploy_dir}.zip"
        with zipfile.ZipFile(archive_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(deploy_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, os.path.dirname(deploy_dir))
                    zipf.write(file_path, arc_path)
        
        print(f"📦 Archive créée: {archive_name}")
        return archive_name
    except Exception as e:
        print(f"⚠️  Impossible de créer l'archive: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Déploiement OfflineMap pour VM hors ligne")
    print("=" * 50)
    
    # Vérifier les prérequis
    if not os.path.exists("tiles"):
        print("❌ Dossier 'tiles' non trouvé!")
        print("   Exécutez d'abord: python download_tiles.py")
        sys.exit(1)
    
    if not os.path.exists("tile_server.py"):
        print("❌ Serveur de tuiles non trouvé!")
        print("   Exécutez d'abord: python configure_offline.py")
        sys.exit(1)
    
    # Créer le package
    deploy_dir = create_deployment_package()
    
    # Créer l'archive
    archive = create_archive(deploy_dir)
    
    print("\n🎉 Déploiement terminé!")
    print(f"📁 Dossier: {deploy_dir}/")
    if archive:
        print(f"📦 Archive: {archive}")
    
    print("\n📋 Pour déployer sur la VM:")
    print("1. Copiez le dossier/archive sur la VM")
    print("2. Décompressez si nécessaire") 
    print("3. Lancez start.bat (Windows) ou ./start.sh (Linux)")
    print("4. Ouvrez http://localhost:8080 dans le navigateur")
