#!/usr/bin/env python3
"""
Script pour télécharger toutes les tuiles de carte nécessaires pour un fonctionnement hors ligne
"""
import os
import json
import requests
import time
from urllib.parse import urlparse

def download_tiles_from_cache_keys(cache_keys_file, output_dir):
    """
    Télécharge toutes les tuiles listées dans cache_keys.json
    """
    # Créer le dossier de sortie
    os.makedirs(output_dir, exist_ok=True)
    
    # Lire les clés de cache
    with open(cache_keys_file, 'r') as f:
        tile_keys = json.load(f)
    
    print(f"Téléchargement de {len(tile_keys)} tuiles...")
    
    for i, key in enumerate(tile_keys):
        # Convertir la clé en URL
        z, x, y = key.split(',')
        tile_url = f"http://tile.osm.org/{z}/{x}/{y}.png"
        
        # Nom du fichier local
        tile_dir = os.path.join(output_dir, z, x)
        os.makedirs(tile_dir, exist_ok=True)
        tile_file = os.path.join(tile_dir, f"{y}.png")
        
        # Télécharger si pas déjà présent
        if not os.path.exists(tile_file):
            try:
                response = requests.get(tile_url, timeout=10)
                response.raise_for_status()
                
                with open(tile_file, 'wb') as f:
                    f.write(response.content)
                
                print(f"[{i+1}/{len(tile_keys)}] Téléchargé: {tile_url}")
                
                # Pause pour éviter de surcharger le serveur
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Erreur pour {tile_url}: {e}")
        else:
            print(f"[{i+1}/{len(tile_keys)}] Déjà présent: {tile_file}")

def create_tile_server_config():
    """
    Crée un serveur de tuiles local simple
    """
    server_code = '''#!/usr/bin/env python3
import http.server
import socketserver
import os
from urllib.parse import urlparse

class TileHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Rediriger les requêtes de tuiles vers les fichiers locaux
        if self.path.startswith('/tiles/'):
            # Format: /tiles/z/x/y.png
            tile_path = self.path[7:]  # Enlever '/tiles/'
            local_path = os.path.join('tiles', tile_path)
            
            if os.path.exists(local_path):
                self.path = '/' + local_path
            else:
                self.send_error(404, "Tuile non trouvée")
                return
        
        super().do_GET()

if __name__ == "__main__":
    PORT = 8080
    with socketserver.TCPServer(("", PORT), TileHandler) as httpd:
        print(f"Serveur de tuiles démarré sur http://localhost:{PORT}")
        print("Ctrl+C pour arrêter")
        httpd.serve_forever()
'''
    
    with open('tile_server.py', 'w') as f:
        f.write(server_code)
    
    print("Serveur de tuiles créé: tile_server.py")

if __name__ == "__main__":
    # Télécharger les tuiles pour chaque exemple
    examples = [
        'leaflet_base64_precache_site',
        'leaflet_base64fr_precache_site', 
        'leaflet_blob_precache_site'
    ]
    
    for example in examples:
        cache_file = os.path.join(example, 'cache_keys.json')
        if os.path.exists(cache_file):
            print(f"\n=== Traitement de {example} ===")
            download_tiles_from_cache_keys(cache_file, 'tiles')
    
    # Créer le serveur de tuiles
    create_tile_server_config()
    
    print("\n✅ Téléchargement terminé!")
    print("📁 Tuiles sauvegardées dans le dossier 'tiles/'")
    print("🚀 Utilisez 'python tile_server.py' pour démarrer le serveur local")
