<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>🗺️ OfflineMap - Démonstration</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .example-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-color: #2196F3;
        }
        
        .example-card h3 {
            margin: 0 0 10px 0;
            color: #2196F3;
            font-size: 1.3em;
        }
        
        .example-card p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.5;
        }
        
        .example-card .tech-tags {
            margin: 10px 0;
        }
        
        .tech-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }
        
        .example-card a {
            display: inline-block;
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
            font-weight: 500;
        }
        
        .example-card a:hover {
            background: #1976D2;
        }
        
        .info-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .info-section h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background: #4CAF50; }
        .status-offline { background: #FF9800; }
        .status-cache { background: #2196F3; }
        
        .instructions {
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #2e7d32;
        }
        
        .server-info {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ OfflineMap Démonstration</h1>
            <p>Exemples de cartes interactives fonctionnant hors ligne</p>
        </div>
        
        <div class="content">
            <div class="server-info">
                <strong>🌐 Serveur local actif:</strong> http://localhost:8000<br>
                <small>Les exemples utilisent actuellement les serveurs de tuiles en ligne. Pour un fonctionnement 100% hors ligne, utilisez les scripts de déploiement.</small>
            </div>
            
            <div class="info-section">
                <h3>💡 Comment tester le cache hors ligne</h3>
                <ol>
                    <li><strong>Naviguez sur la carte</strong> - Les tuiles se mettent automatiquement en cache</li>
                    <li><strong>Coupez votre connexion internet</strong> ou désactivez le réseau</li>
                    <li><strong>Rechargez la page</strong> - Les zones visitées restent visibles !</li>
                    <li><strong>Utilisez le bouton "C"</strong> (si disponible) pour pré-charger une zone</li>
                </ol>
            </div>
            
            <div class="examples-grid">
                <div class="example-card">
                    <h3><span class="status-indicator status-cache"></span>Leaflet + IndexedDB/WebSQL</h3>
                    <p>Exemple utilisant Leaflet avec stockage local via IndexedDB ou WebSQL. Cache automatique des tuiles visitées.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">Leaflet</span>
                        <span class="tech-tag">IndexedDB</span>
                        <span class="tech-tag">WebSQL</span>
                    </div>
                    <a href="leaflet_idb_sql_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
                
                <div class="example-card">
                    <h3><span class="status-indicator status-cache"></span>Leaflet + PouchDB (Base64)</h3>
                    <p>Exemple avec PouchDB et stockage des tuiles en Base64. Bouton "C" pour pré-charger les tuiles.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">Leaflet</span>
                        <span class="tech-tag">PouchDB</span>
                        <span class="tech-tag">Base64</span>
                    </div>
                    <a href="leaflet_base64_precache_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
                
                <div class="example-card">
                    <h3><span class="status-indicator status-cache"></span>Leaflet + PouchDB (Blob)</h3>
                    <p>Similaire au précédent mais utilise le stockage Blob pour une meilleure performance.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">Leaflet</span>
                        <span class="tech-tag">PouchDB</span>
                        <span class="tech-tag">Blob</span>
                    </div>
                    <a href="leaflet_blob_precache_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
                
                <div class="example-card">
                    <h3><span class="status-indicator status-cache"></span>Mapbox + IndexedDB/WebSQL</h3>
                    <p>Exemple utilisant Mapbox (Modest Maps) avec stockage local des tuiles.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">Mapbox</span>
                        <span class="tech-tag">IndexedDB</span>
                        <span class="tech-tag">WebSQL</span>
                    </div>
                    <a href="mapbox_idb_sql_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
                
                <div class="example-card">
                    <h3><span class="status-indicator status-cache"></span>OpenLayers + IndexedDB/WebSQL</h3>
                    <p>Exemple avec OpenLayers et système de cache local pour les tuiles.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">OpenLayers</span>
                        <span class="tech-tag">IndexedDB</span>
                        <span class="tech-tag">WebSQL</span>
                    </div>
                    <a href="openlayers_idb_sql_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
                
                <div class="example-card">
                    <h3><span class="status-indicator status-offline"></span>Google Maps + LocalStorage</h3>
                    <p>Exemple Google Maps avec LocalStorage. ⚠️ Nécessite une clé API Google Maps valide.</p>
                    <div class="tech-tags">
                        <span class="tech-tag">Google Maps</span>
                        <span class="tech-tag">LocalStorage</span>
                        <span class="tech-tag">API Key Required</span>
                    </div>
                    <a href="gmaps_localstorage_site/index.html" target="_blank">🚀 Ouvrir l'exemple</a>
                </div>
            </div>
            
            <div class="instructions">
                <h4>🧪 Instructions de test</h4>
                <p><strong>1. Cache automatique :</strong> Naviguez sur les cartes, les tuiles se mettent automatiquement en cache</p>
                <p><strong>2. Pré-cache :</strong> Cliquez sur le bouton "C" (si disponible) pour télécharger les tuiles d'une zone</p>
                <p><strong>3. Test hors ligne :</strong> Coupez internet et rechargez - les zones visitées restent visibles !</p>
                <p><strong>4. Outils développeur :</strong> F12 → Application → Storage pour voir les données en cache</p>
            </div>
        </div>
    </div>
    
    <script>
        // Vérifier le statut du serveur
        console.log('🗺️ OfflineMap Demo loaded');
        console.log('Server: http://localhost:8000');
        
        // Ajouter des événements pour les liens
        document.querySelectorAll('a[target="_blank"]').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('Opening:', this.href);
            });
        });
    </script>
</body>
</html>
