# 🗺️ Guide de Test - OfflineMap

## 🚀 Application lancée !

✅ **Serveur local actif :** http://localhost:8000  
✅ **Page d'accueil :** http://localhost:8000/demo_index.html

## 🧪 Comment tester les fonctionnalités hors ligne

### **1. Test basique du cache automatique**

1. **Ouvrez un exemple** (ex: Leaflet + PouchDB Base64)
2. **Naviguez sur la carte** - zoomez, déplacez-vous
3. **Ouvrez les outils développeur** (F12)
4. **Allez dans Application → Storage → IndexedDB**
5. **Vous verrez la base "tile" avec les tuiles en cache !**

### **2. Test du pré-cache (bouton "C")**

1. **Ouvrez un exemple avec bouton "C"** (PouchDB examples)
2. **Cliquez sur le bouton "C"** en haut à gauche
3. **Attendez le téléchargement** (regardez la console F12)
4. **Les tuiles se chargent automatiquement en cache**

### **3. Test hors ligne complet**

1. **Naviguez d'abord sur la carte** pour mettre en cache
2. **Coupez votre connexion internet** (WiFi/Ethernet)
3. **Rechargez la page** (F5)
4. **🎉 Les zones visitées restent visibles !**

### **4. Inspection du cache**

**Dans les outils développeur (F12) :**

- **Application → Storage → IndexedDB → tile**
- **Application → Storage → Local Storage**
- **Application → Storage → WebSQL** (si supporté)

## 📋 Exemples disponibles

| Exemple | Technologie | Bouton Cache | Statut |
|---------|-------------|--------------|--------|
| **Leaflet + IndexedDB/WebSQL** | IndexedDB, WebSQL | ❌ | ✅ Auto-cache |
| **Leaflet + PouchDB (Base64)** | PouchDB, Base64 | ✅ "C" | ✅ Pré-cache |
| **Leaflet + PouchDB (Blob)** | PouchDB, Blob | ✅ "C" | ✅ Pré-cache |
| **Leaflet + PouchDB (Base64 FR)** | PouchDB, FileReader | ✅ "C" | ✅ Pré-cache |
| **Mapbox + IndexedDB/WebSQL** | Mapbox, IndexedDB | ❌ | ✅ Auto-cache |
| **OpenLayers + IndexedDB/WebSQL** | OpenLayers, IndexedDB | ❌ | ✅ Auto-cache |
| **Google Maps + LocalStorage** | Google Maps, LocalStorage | ❌ | ⚠️ API Key requis |

## 🔧 Fonctionnalités par exemple

### **Leaflet + PouchDB (Recommandé pour les tests)**
- ✅ **Bouton "C"** pour pré-charger les tuiles
- ✅ **Cache automatique** pendant la navigation
- ✅ **Stockage Base64** ou **Blob**
- ✅ **Fonctionne 100% hors ligne**

### **Leaflet + IndexedDB/WebSQL**
- ✅ **Cache automatique** uniquement
- ✅ **Stockage IndexedDB** (moderne)
- ✅ **Fallback WebSQL** (ancien)
- ✅ **Performance optimisée**

## 🎯 Test recommandé

**Pour voir rapidement le système en action :**

1. **Ouvrez :** http://localhost:8000/leaflet_base64_precache_site/index.html
2. **Cliquez sur "C"** → Les tuiles se téléchargent
3. **F12 → Application → IndexedDB → tile** → Voir les données
4. **Coupez internet → Rechargez** → ça marche toujours !

## 🚨 Dépannage

**Si les cartes ne s'affichent pas :**
- Vérifiez la connexion internet (pour le premier chargement)
- Ouvrez F12 → Console pour voir les erreurs
- Vérifiez que le serveur tourne sur port 8000

**Si le cache ne fonctionne pas :**
- Vérifiez F12 → Application → Storage
- Essayez un autre navigateur (Chrome/Firefox)
- Videz le cache navigateur et recommencez

## 📊 Données techniques

**Localisation des tuiles :** Minsk, Belarus (53.902254, 27.561850)  
**Niveaux de zoom :** 0-15  
**Source des tuiles :** OpenStreetMap  
**Taille du cache :** Variable selon navigation

---

**🎉 Amusez-vous bien avec les cartes hors ligne !**
